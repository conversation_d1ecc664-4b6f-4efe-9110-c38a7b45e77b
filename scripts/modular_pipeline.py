#!/usr/bin/env python3
"""
模块化数据处理流水线
基于阶段一的配置，支持DataFrame流式处理

作者: Assistant
日期: 2024
"""

import asyncio
import logging
import pandas as pd
import time
import psutil
import os
import sys
import io
import pickle
import hashlib
from pathlib import Path
from contextlib import redirect_stdout, redirect_stderr
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta

# 导入阶段一的配置
from pipeline_config import PipelineConfig

# 导入各个处理模块
from convert_jsonl_to_csv import JSONLToCSVConverter
from update_csv_with_brain_api import CSVUpdater as Brain<PERSON>IUpdater
from update_csv_with_clickhouse import CSVUpdater as ClickHouseUpdater
from deduplicate_and_update_prices import deduplicate_and_update_dataframe


@dataclass
class CacheStats:
    """Cache statistics for monitoring performance"""
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    cache_size: int = 0
    last_updated: Optional[datetime] = None

    @property
    def hit_ratio(self) -> float:
        """Calculate cache hit ratio"""
        if self.total_requests == 0:
            return 0.0
        return self.cache_hits / self.total_requests

    @property
    def miss_ratio(self) -> float:
        """Calculate cache miss ratio"""
        return 1.0 - self.hit_ratio


class PersistentCache:
    """Persistent local cache using pickle files with TTL support"""

    def __init__(self, cache_dir: Path, cache_name: str, ttl_hours: int = 24):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.cache_file = self.cache_dir / f"{cache_name}.pkl"
        self.metadata_file = self.cache_dir / f"{cache_name}_metadata.pkl"
        self.ttl_hours = ttl_hours
        self.stats = CacheStats()
        self._cache_data = {}
        self._metadata = {}
        self._load_cache()

    def _load_cache(self):
        """Load cache from disk"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    self._cache_data = pickle.load(f)

            if self.metadata_file.exists():
                with open(self.metadata_file, 'rb') as f:
                    self._metadata = pickle.load(f)

            # Update stats
            self.stats.cache_size = len(self._cache_data)
            if self._metadata.get('last_updated'):
                self.stats.last_updated = self._metadata['last_updated']

        except Exception as e:
            # If cache is corrupted, start fresh
            self._cache_data = {}
            self._metadata = {}

    def _save_cache(self):
        """Save cache to disk"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self._cache_data, f)

            self._metadata['last_updated'] = datetime.now()
            self._metadata['cache_size'] = len(self._cache_data)

            with open(self.metadata_file, 'wb') as f:
                pickle.dump(self._metadata, f)

            self.stats.last_updated = self._metadata['last_updated']
            self.stats.cache_size = len(self._cache_data)

        except Exception as e:
            # Log error but don't fail
            pass

    def _is_expired(self, key: str) -> bool:
        """Check if cache entry is expired"""
        if key not in self._metadata:
            return True

        entry_time = self._metadata.get(key, {}).get('timestamp')
        if not entry_time:
            return True

        expiry_time = entry_time + timedelta(hours=self.ttl_hours)
        return datetime.now() > expiry_time

    def get(self, key: str) -> Optional[any]:
        """Get value from cache"""
        self.stats.total_requests += 1

        if key in self._cache_data and not self._is_expired(key):
            self.stats.cache_hits += 1
            return self._cache_data[key]

        self.stats.cache_misses += 1
        return None

    def set(self, key: str, value: any):
        """Set value in cache"""
        self._cache_data[key] = value
        if key not in self._metadata:
            self._metadata[key] = {}
        self._metadata[key]['timestamp'] = datetime.now()
        self.stats.cache_size = len(self._cache_data)

    def get_multiple(self, keys: List[str]) -> Dict[str, any]:
        """Get multiple values from cache"""
        result = {}
        for key in keys:
            value = self.get(key)
            if value is not None:
                result[key] = value
        return result

    def set_multiple(self, data: Dict[str, any]):
        """Set multiple values in cache"""
        for key, value in data.items():
            self.set(key, value)

    def clear_expired(self):
        """Remove expired entries from cache"""
        expired_keys = [key for key in self._cache_data.keys() if self._is_expired(key)]
        for key in expired_keys:
            del self._cache_data[key]
            if key in self._metadata:
                del self._metadata[key]
        self.stats.cache_size = len(self._cache_data)

    def clear_all(self):
        """Clear all cache entries"""
        self._cache_data.clear()
        self._metadata.clear()
        self.stats = CacheStats()

    def save(self):
        """Manually save cache to disk"""
        self._save_cache()

    def get_missing_keys(self, keys: List[str]) -> List[str]:
        """Get list of keys that are not in cache or expired"""
        return [key for key in keys if self.get(key) is None]


class OptimizedBrainAPIProcessor:
    """Enhanced Brain API processor with improved error handling and validation"""

    def __init__(self, brain_updater: BrainAPIUpdater, cache: PersistentCache,
                 batch_size: int = 10000, max_concurrent: int = 3):
        self.brain_updater = brain_updater
        self.cache = cache
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.logger = logging.getLogger(__name__)

        # Enhanced error tracking
        self.error_stats = {
            'api_failures': 0,
            'invalid_smiles': 0,
            'timeout_errors': 0,
            'network_errors': 0,
            'validation_errors': 0,
            'missing_results': 0
        }

        # Retry configuration
        self.max_retries = 3
        self.retry_delay = 2.0
        self.backoff_multiplier = 2.0

    async def process_dataframe_optimized(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """Process DataFrame with enhanced SMILES validation and error handling"""
        self.logger.info("开始增强的Brain API处理...")

        # Step 1: Validate and extract unique SMILES with preprocessing
        unique_smiles, smiles_mapping = self._extract_and_validate_smiles(df)
        original_count = len(df)
        unique_count = len(unique_smiles)

        self.logger.info(f"原始数据行数: {original_count:,}")
        self.logger.info(f"唯一有效SMILES数量: {unique_count:,}")
        if unique_count > 0:
            self.logger.info(f"去重率: {(1 - unique_count/original_count)*100:.1f}%")

        # Step 2: Check cache for existing results
        cached_results = self.cache.get_multiple(unique_smiles)
        missing_smiles = self.cache.get_missing_keys(unique_smiles)

        cache_hits = len(cached_results)
        cache_misses = len(missing_smiles)

        if unique_count > 0:
            self.logger.info(f"缓存命中: {cache_hits:,} ({cache_hits/unique_count*100:.1f}%)")
            self.logger.info(f"缓存未命中: {cache_misses:,} ({cache_misses/unique_count*100:.1f}%)")

        # Step 3: Process missing SMILES with enhanced error handling
        new_results = {}
        if missing_smiles:
            self.logger.info(f"开始处理 {len(missing_smiles):,} 个新SMILES...")
            new_results = await self._process_smiles_batches_enhanced(missing_smiles)

            # Update cache with successful results only
            successful_results = {k: v for k, v in new_results.items() if v.get('success', False)}
            if successful_results:
                self.cache.set_multiple(successful_results)
                self.cache.save()

            self.logger.info(f"新处理完成: {len(new_results):,} 个SMILES (成功: {len(successful_results)})")

        # Step 4: Combine cached and new results
        all_results = {**cached_results, **new_results}

        # Step 5: Apply results with comprehensive error tracking
        updated_df, error_records = self._apply_results_to_dataframe_enhanced(df, all_results, smiles_mapping)

        # Step 6: Log detailed statistics
        self._log_processing_statistics(original_count, len(updated_df), len(error_records))

        return updated_df, error_records

    def _extract_and_validate_smiles(self, df: pd.DataFrame) -> Tuple[List[str], Dict[str, List[int]]]:
        """Extract and validate SMILES with row index mapping"""
        smiles_mapping = {}  # smiles -> list of row indices
        valid_smiles = set()

        for idx, row in df.iterrows():
            smiles = row.get('canonical_smiles', '').strip()

            # Validate SMILES
            if self._is_valid_smiles(smiles):
                if smiles not in smiles_mapping:
                    smiles_mapping[smiles] = []
                smiles_mapping[smiles].append(idx)
                valid_smiles.add(smiles)
            else:
                # Track invalid SMILES for error reporting
                if smiles:
                    self.error_stats['invalid_smiles'] += 1
                    self.logger.debug(f"Invalid SMILES detected: '{smiles}' at row {idx}")

        unique_smiles = list(valid_smiles)
        self.logger.info(f"SMILES验证完成: 有效 {len(unique_smiles)}, 无效 {self.error_stats['invalid_smiles']}")

        return unique_smiles, smiles_mapping

    def _is_valid_smiles(self, smiles: str) -> bool:
        """Basic SMILES validation"""
        if not smiles or not isinstance(smiles, str):
            return False

        smiles = smiles.strip()
        if not smiles:
            return False

        # Basic validation rules
        if len(smiles) > 1000:  # Extremely long SMILES are likely invalid
            return False

        # Check for obvious invalid characters (basic check)
        invalid_chars = set(['<', '>', '{', '}', '|', '\\', '"', "'"])
        if any(char in smiles for char in invalid_chars):
            return False

        return True

    async def _process_smiles_batches_enhanced(self, smiles_list: List[str]) -> Dict[str, any]:
        """Enhanced batch processing with comprehensive error handling"""
        results = {}

        # Create batches
        batches = [smiles_list[i:i + self.batch_size]
                  for i in range(0, len(smiles_list), self.batch_size)]

        self.logger.info(f"分成 {len(batches)} 个批次处理，每批最多 {self.batch_size} 个SMILES")

        # Process batches with enhanced error handling
        for i, batch in enumerate(batches):
            batch_results = await self._process_single_batch_with_retry(batch, i + 1, len(batches))

            # Ensure all SMILES in batch have results
            for smiles in batch:
                if smiles not in batch_results:
                    self.error_stats['missing_results'] += 1
                    batch_results[smiles] = {
                        'canonical_smiles': smiles,
                        'inchified_smiles': None,
                        'success': False,
                        'error': 'No result returned from API',
                        'error_category': 'missing_result'
                    }

            results.update(batch_results)

        return results

    async def _process_smiles_batches(self, smiles_list: List[str]) -> Dict[str, any]:
        """Process SMILES in concurrent batches"""
        results = {}

        # Create batches
        batches = [smiles_list[i:i + self.batch_size]
                  for i in range(0, len(smiles_list), self.batch_size)]

        self.logger.info(f"分成 {len(batches)} 个批次处理，每批 {self.batch_size} 个SMILES")

        # Process batches concurrently
        tasks = []
        for i, batch in enumerate(batches):
            task = self._process_single_batch(batch, i + 1, len(batches))
            tasks.append(task)

        # Wait for all batches to complete
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Combine results
        for batch_result in batch_results:
            if isinstance(batch_result, Exception):
                self.logger.error(f"批次处理失败: {batch_result}")
                continue
            results.update(batch_result)

        return results

    async def _process_single_batch_with_retry(self, batch_smiles: List[str],
                                             batch_num: int, total_batches: int) -> Dict[str, any]:
        """Process a single batch with retry logic and enhanced error handling"""
        for attempt in range(self.max_retries + 1):
            try:
                async with self.semaphore:
                    self.logger.info(f"处理批次 {batch_num}/{total_batches} ({len(batch_smiles)} 个SMILES) - 尝试 {attempt + 1}")

                    # Create temporary DataFrame for this batch
                    temp_df = pd.DataFrame({'canonical_smiles': batch_smiles})

                    # Process with existing brain updater
                    result_df, errors = await self.brain_updater.update_dataframe(temp_df)

                    # Enhanced result processing
                    batch_results = self._process_batch_results(batch_smiles, result_df, errors, batch_num)

                    # Validate that all SMILES have results
                    missing_smiles = set(batch_smiles) - set(batch_results.keys())
                    if missing_smiles:
                        self.logger.warning(f"批次 {batch_num}: {len(missing_smiles)} 个SMILES缺少结果")
                        for smiles in missing_smiles:
                            batch_results[smiles] = {
                                'canonical_smiles': smiles,
                                'inchified_smiles': None,
                                'success': False,
                                'error': 'Missing from API response',
                                'error_category': 'api_missing'
                            }

                    success_count = sum(1 for r in batch_results.values() if r.get('success', False))
                    self.logger.info(f"批次 {batch_num} 完成: 成功 {success_count}/{len(batch_smiles)}")

                    return batch_results

            except asyncio.TimeoutError as e:
                self.error_stats['timeout_errors'] += 1
                self.logger.warning(f"批次 {batch_num} 超时 (尝试 {attempt + 1}): {e}")
                if attempt < self.max_retries:
                    await asyncio.sleep(self.retry_delay * (self.backoff_multiplier ** attempt))
                    continue

            except Exception as e:
                self.error_stats['api_failures'] += 1
                self.logger.error(f"批次 {batch_num} 处理异常 (尝试 {attempt + 1}): {e}")
                if attempt < self.max_retries:
                    await asyncio.sleep(self.retry_delay * (self.backoff_multiplier ** attempt))
                    continue

        # All retries failed, return error results for entire batch
        self.logger.error(f"批次 {batch_num} 所有重试失败，标记整批为错误")
        return {smiles: {
            'canonical_smiles': smiles,
            'inchified_smiles': None,
            'success': False,
            'error': f'Batch processing failed after {self.max_retries + 1} attempts',
            'error_category': 'batch_failure'
        } for smiles in batch_smiles}

    def _process_batch_results(self, batch_smiles: List[str], result_df: pd.DataFrame,
                              errors: List[Dict], batch_num: int) -> Dict[str, any]:
        """Process batch results with enhanced validation"""
        batch_results = {}

        # Process successful results
        for _, row in result_df.iterrows():
            original_smiles = row.get('canonical_smiles')
            if original_smiles:
                batch_results[original_smiles] = {
                    'canonical_smiles': row.get('canonical_smiles'),
                    'inchified_smiles': row.get('inchified_smiles'),
                    'success': True,
                    'error_category': None
                }

        # Process errors with categorization
        for error in errors:
            if 'canonical_smiles' in error:
                smiles = error['canonical_smiles']
                error_reason = error.get('error_reason', 'Unknown error')
                error_category = self._categorize_error(error_reason)

                batch_results[smiles] = {
                    'canonical_smiles': smiles,
                    'inchified_smiles': None,
                    'success': False,
                    'error': error_reason,
                    'error_category': error_category
                }

                # Update error statistics
                if error_category == 'invalid_smiles':
                    self.error_stats['invalid_smiles'] += 1
                elif error_category == 'api_error':
                    self.error_stats['api_failures'] += 1
                elif error_category == 'validation_error':
                    self.error_stats['validation_errors'] += 1

        return batch_results

    def _categorize_error(self, error_reason: str) -> str:
        """Categorize error reasons for better tracking"""
        error_reason_lower = error_reason.lower()

        if 'invalid' in error_reason_lower or 'malformed' in error_reason_lower:
            return 'invalid_smiles'
        elif 'timeout' in error_reason_lower:
            return 'timeout'
        elif 'network' in error_reason_lower or 'connection' in error_reason_lower:
            return 'network_error'
        elif 'api' in error_reason_lower:
            return 'api_error'
        elif 'validation' in error_reason_lower:
            return 'validation_error'
        else:
            return 'unknown'

    async def _process_single_batch(self, batch_smiles: List[str],
                                   batch_num: int, total_batches: int) -> Dict[str, any]:
        """Process a single batch of SMILES with semaphore control"""
        async with self.semaphore:
            self.logger.info(f"处理批次 {batch_num}/{total_batches} ({len(batch_smiles)} 个SMILES)")

            try:
                # Create temporary DataFrame for this batch
                temp_df = pd.DataFrame({'canonical_smiles': batch_smiles})

                # Process with existing brain updater
                result_df, errors = await self.brain_updater.update_dataframe(temp_df)

                # Convert results to dictionary format
                batch_results = {}
                for _, row in result_df.iterrows():
                    original_smiles = row['canonical_smiles']
                    batch_results[original_smiles] = {
                        'canonical_smiles': row.get('canonical_smiles'),
                        'inchified_smiles': row.get('inchified_smiles'),
                        'success': True
                    }

                # Handle errors
                for error in errors:
                    if 'canonical_smiles' in error:
                        smiles = error['canonical_smiles']
                        batch_results[smiles] = {
                            'canonical_smiles': smiles,
                            'inchified_smiles': None,
                            'success': False,
                            'error': error.get('error_reason', 'Unknown error')
                        }

                self.logger.info(f"批次 {batch_num} 完成: 成功 {len(result_df)}, 错误 {len(errors)}")
                return batch_results

            except Exception as e:
                self.logger.error(f"批次 {batch_num} 处理异常: {e}")
                # Return error results for this batch
                return {smiles: {
                    'canonical_smiles': smiles,
                    'inchified_smiles': None,
                    'success': False,
                    'error': str(e)
                } for smiles in batch_smiles}

    def _apply_results_to_dataframe_enhanced(self, df: pd.DataFrame,
                                           results: Dict[str, any],
                                           smiles_mapping: Dict[str, List[int]]) -> Tuple[pd.DataFrame, List[Dict]]:
        """Apply API results with enhanced error tracking and validation"""
        updated_rows = []
        error_records = []

        for idx, row in df.iterrows():
            row_dict = row.to_dict()
            smiles = row_dict.get('canonical_smiles', '').strip()

            if not smiles:
                # Empty SMILES
                row_dict['error_reason'] = 'Empty canonical_smiles field'
                row_dict['error_category'] = 'empty_smiles'
                error_records.append(row_dict)
                continue

            if smiles in results:
                result = results[smiles]
                if result.get('success', False):
                    # Successful processing
                    row_dict['canonical_smiles'] = result.get('canonical_smiles', smiles)
                    row_dict['inchified_smiles'] = result.get('inchified_smiles')
                    updated_rows.append(row_dict)
                else:
                    # API processing failed
                    row_dict['error_reason'] = result.get('error', 'API processing failed')
                    row_dict['error_category'] = result.get('error_category', 'unknown')
                    error_records.append(row_dict)
            else:
                # This should not happen with enhanced processing
                self.error_stats['missing_results'] += 1
                row_dict['error_reason'] = 'No API result found - internal processing error'
                row_dict['error_category'] = 'internal_error'
                error_records.append(row_dict)
                self.logger.error(f"Missing result for SMILES: '{smiles}' at row {idx}")

        updated_df = pd.DataFrame(updated_rows) if updated_rows else pd.DataFrame()
        return updated_df, error_records

    def _log_processing_statistics(self, original_count: int, success_count: int, error_count: int):
        """Log detailed processing statistics"""
        total_processed = success_count + error_count
        success_rate = (success_count / total_processed * 100) if total_processed > 0 else 0

        self.logger.info("=" * 60)
        self.logger.info("Brain API处理统计:")
        self.logger.info(f"  原始记录数: {original_count:,}")
        self.logger.info(f"  成功处理: {success_count:,}")
        self.logger.info(f"  处理失败: {error_count:,}")
        self.logger.info(f"  成功率: {success_rate:.1f}%")

        if any(self.error_stats.values()):
            self.logger.info("错误分类统计:")
            for error_type, count in self.error_stats.items():
                if count > 0:
                    self.logger.info(f"  {error_type}: {count:,}")

        self.logger.info("=" * 60)

    def get_error_statistics(self) -> Dict[str, int]:
        """Get detailed error statistics"""
        return self.error_stats.copy()

    def _apply_results_to_dataframe(self, df: pd.DataFrame,
                                   results: Dict[str, any]) -> Tuple[pd.DataFrame, List[Dict]]:
        """Apply API results back to the original DataFrame"""
        updated_rows = []
        error_records = []

        for _, row in df.iterrows():
            row_dict = row.to_dict()
            smiles = row_dict.get('canonical_smiles')

            if smiles in results:
                result = results[smiles]
                if result.get('success', False):
                    row_dict['canonical_smiles'] = result.get('canonical_smiles', smiles)
                    row_dict['inchified_smiles'] = result.get('inchified_smiles')
                    updated_rows.append(row_dict)
                else:
                    row_dict['error_reason'] = result.get('error', 'API processing failed')
                    error_records.append(row_dict)
            else:
                # No result found (shouldn't happen)
                row_dict['error_reason'] = 'No API result found'
                error_records.append(row_dict)

        updated_df = pd.DataFrame(updated_rows) if updated_rows else pd.DataFrame()
        return updated_df, error_records


class OptimizedClickHouseProcessor:
    """Optimized ClickHouse processor with caching and batch processing"""

    def __init__(self, clickhouse_updater: ClickHouseUpdater, cache: PersistentCache,
                 batch_size: int = 10000):
        self.clickhouse_updater = clickhouse_updater
        self.cache = cache
        self.batch_size = batch_size
        self.logger = logging.getLogger(__name__)

    def process_dataframe_optimized(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """Process DataFrame with inchified_smiles deduplication and caching"""
        self.logger.info("开始优化的ClickHouse处理...")

        # Step 1: Extract unique inchified_smiles
        unique_inchi_smiles = df['inchified_smiles'].dropna().unique().tolist()
        original_count = len(df)
        unique_count = len(unique_inchi_smiles)

        self.logger.info(f"原始数据行数: {original_count:,}")
        self.logger.info(f"唯一inchified_smiles数量: {unique_count:,}")
        self.logger.info(f"去重率: {(1 - unique_count/original_count)*100:.1f}%")

        # Step 2: Check cache for existing results
        cached_results = self.cache.get_multiple(unique_inchi_smiles)
        missing_inchi_smiles = self.cache.get_missing_keys(unique_inchi_smiles)

        cache_hits = len(cached_results)
        cache_misses = len(missing_inchi_smiles)

        self.logger.info(f"缓存命中: {cache_hits:,} ({cache_hits/unique_count*100:.1f}%)")
        self.logger.info(f"缓存未命中: {cache_misses:,} ({cache_misses/unique_count*100:.1f}%)")

        # Step 3: Process missing inchified_smiles in batches
        new_results = {}
        if missing_inchi_smiles:
            self.logger.info(f"开始处理 {len(missing_inchi_smiles):,} 个新inchified_smiles...")
            new_results = self._process_inchi_smiles_batches(missing_inchi_smiles)

            # Update cache with new results
            self.cache.set_multiple(new_results)
            self.cache.save()

            self.logger.info(f"新处理完成: {len(new_results):,} 个inchified_smiles")

        # Step 4: Combine cached and new results
        all_results = {**cached_results, **new_results}

        # Step 5: Apply results back to original DataFrame
        updated_df, error_records = self._apply_results_to_dataframe(df, all_results)

        self.logger.info(f"ClickHouse处理完成: 成功 {len(updated_df):,} 行, 错误 {len(error_records):,} 条")

        return updated_df, error_records

    def _process_inchi_smiles_batches(self, inchi_smiles_list: List[str]) -> Dict[str, any]:
        """Process inchified_smiles in batches"""
        results = {}

        # Create batches
        batches = [inchi_smiles_list[i:i + self.batch_size]
                  for i in range(0, len(inchi_smiles_list), self.batch_size)]

        self.logger.info(f"分成 {len(batches)} 个批次处理，每批 {self.batch_size} 个inchified_smiles")

        # Process batches sequentially (ClickHouse doesn't need async)
        for i, batch in enumerate(batches):
            batch_results = self._process_single_clickhouse_batch(batch, i + 1, len(batches))
            results.update(batch_results)

        return results

    def _process_single_clickhouse_batch(self, batch_inchi_smiles: List[str],
                                        batch_num: int, total_batches: int) -> Dict[str, any]:
        """Process a single batch of inchified_smiles"""
        self.logger.info(f"处理ClickHouse批次 {batch_num}/{total_batches} ({len(batch_inchi_smiles)} 个inchified_smiles)")

        try:
            # Query ClickHouse for this batch
            hazards_dict = self.clickhouse_updater.query_hazards_batch(batch_inchi_smiles)

            # Convert to standardized format
            batch_results = {}
            for inchi_smi in batch_inchi_smiles:
                if inchi_smi in hazards_dict:
                    hazard_info = hazards_dict[inchi_smi]
                    batch_results[inchi_smi] = {
                        'cid': hazard_info.get('cid'),
                        'hazards': hazard_info.get('hazards'),
                        'success': True
                    }
                else:
                    batch_results[inchi_smi] = {
                        'cid': None,
                        'hazards': None,
                        'success': False,
                        'error': 'Not found in ClickHouse'
                    }

            self.logger.info(f"ClickHouse批次 {batch_num} 完成: 找到 {len(hazards_dict)} 条记录")
            return batch_results

        except Exception as e:
            self.logger.error(f"ClickHouse批次 {batch_num} 处理异常: {e}")
            # Return error results for this batch
            return {inchi_smi: {
                'cid': None,
                'hazards': None,
                'success': False,
                'error': str(e)
            } for inchi_smi in batch_inchi_smiles}

    def _apply_results_to_dataframe(self, df: pd.DataFrame,
                                   results: Dict[str, any]) -> Tuple[pd.DataFrame, List[Dict]]:
        """Apply ClickHouse results back to the original DataFrame"""
        updated_rows = []
        error_records = []

        for _, row in df.iterrows():
            row_dict = row.to_dict()
            inchi_smi = row_dict.get('inchified_smiles')

            if inchi_smi in results:
                result = results[inchi_smi]
                if result.get('success', False):
                    # Add hazard information
                    hazards = result.get('hazards', '')
                    row_dict['codes'] = hazards.split('|') if hazards else []

                    cid = result.get('cid')
                    if cid:
                        row_dict['pubchem_safety_link'] = f"https://pubchem.ncbi.nlm.nih.gov/compound/{cid}#section=Safety-and-Hazards"
                    else:
                        row_dict['pubchem_safety_link'] = ''

                    updated_rows.append(row_dict)
                else:
                    # No hazard data found, but keep the row
                    row_dict['codes'] = []
                    row_dict['pubchem_safety_link'] = ''
                    updated_rows.append(row_dict)
            else:
                # No result found (shouldn't happen)
                row_dict['codes'] = []
                row_dict['pubchem_safety_link'] = ''
                row_dict['error_reason'] = 'No ClickHouse result found'
                error_records.append(row_dict)

        updated_df = pd.DataFrame(updated_rows) if updated_rows else pd.DataFrame()
        return updated_df, error_records


@dataclass
class ModularPipelineStats:
    """模块化流水线统计信息"""

    total_start_time: float
    total_end_time: float
    step_times: Dict[str, float]
    memory_usage: Dict[str, float]
    data_counts: Dict[str, int]
    error_counts: Dict[str, int]

    @property
    def total_duration(self) -> float:
        return self.total_end_time - self.total_start_time

    def get_step_duration(self, step: str) -> float:
        return self.step_times.get(step, 0.0)


class ModularPipelineMonitor:
    """模块化流水线监控器"""

    def __init__(self):
        self.stats = ModularPipelineStats(
            total_start_time=0,
            total_end_time=0,
            step_times={},
            memory_usage={},
            data_counts={},
            error_counts={},
        )
        self.step_start_times = {}

    def start_pipeline(self):
        """开始流水线监控"""
        self.stats.total_start_time = time.time()
        self._log_memory_usage("pipeline_start")

    def end_pipeline(self):
        """结束流水线监控"""
        self.stats.total_end_time = time.time()
        self._log_memory_usage("pipeline_end")

    def start_step(self, step_name: str):
        """开始步骤监控"""
        self.step_start_times[step_name] = time.time()
        self._log_memory_usage(f"{step_name}_start")

    def end_step(self, step_name: str, data_count: int = 0, error_count: int = 0):
        """结束步骤监控"""
        if step_name in self.step_start_times:
            duration = time.time() - self.step_start_times[step_name]
            self.stats.step_times[step_name] = duration
            self.stats.data_counts[step_name] = data_count
            self.stats.error_counts[step_name] = error_count
            self._log_memory_usage(f"{step_name}_end")

    def _log_memory_usage(self, checkpoint: str):
        """记录内存使用情况"""
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        self.stats.memory_usage[checkpoint] = memory_mb

    def print_summary(self):
        """打印监控摘要"""
        print("\n" + "=" * 60)
        print("模块化流水线执行摘要")
        print("=" * 60)
        print(f"总执行时间: {self.stats.total_duration:.2f} 秒")
        print(
            f"内存使用: {self.stats.memory_usage.get('pipeline_start', 0):.1f} MB -> {self.stats.memory_usage.get('pipeline_end', 0):.1f} MB"
        )
        print("\n各步骤详情:")

        for step, duration in self.stats.step_times.items():
            data_count = self.stats.data_counts.get(step, 0)
            error_count = self.stats.error_counts.get(step, 0)
            success_rate = (
                ((data_count - error_count) / data_count * 100) if data_count > 0 else 0
            )
            print(
                f"  {step}: {duration:.2f}s | 数据: {data_count} | 错误: {error_count} | 成功率: {success_rate:.1f}%"
            )

        print("=" * 60)


class OptimizedModularPipeline:
    """优化的模块化数据处理流水线，支持缓存和并发处理"""

    def __init__(self, config: PipelineConfig):
        self.config = config
        self.monitor = ModularPipelineMonitor()
        self.logger = self._setup_logging()

        # 用于捕获子模块输出的缓冲区
        self.output_buffer = io.StringIO()
        self.error_buffer = io.StringIO()

        # 初始化各个处理器
        self.jsonl_converter = JSONLToCSVConverter()
        self.brain_updater = BrainAPIUpdater(batch_size=config.brain_api_batch_size)
        self.clickhouse_updater = ClickHouseUpdater(
            clickhouse_host=config.clickhouse_host
        )

        # 初始化缓存系统
        cache_dir = getattr(config, 'cache_dir', config.temp_dir / 'cache')
        cache_ttl = getattr(config, 'cache_ttl_hours', 24)

        self.brain_cache = PersistentCache(cache_dir, 'brain_api_cache', cache_ttl)
        self.clickhouse_cache = PersistentCache(cache_dir, 'clickhouse_cache', cache_ttl)

        # 初始化优化处理器
        brain_batch_size = getattr(config, 'brain_api_batch_size', 10000)
        brain_max_concurrent = getattr(config, 'brain_api_max_concurrent', 3)
        clickhouse_batch_size = getattr(config, 'clickhouse_batch_size', 10000)

        self.optimized_brain_processor = OptimizedBrainAPIProcessor(
            self.brain_updater, self.brain_cache, brain_batch_size, brain_max_concurrent
        )

        self.optimized_clickhouse_processor = OptimizedClickHouseProcessor(
            self.clickhouse_updater, self.clickhouse_cache, clickhouse_batch_size
        )

        # 准备目录
        self.prepare_directories()

        # 配置子模块日志
        self._setup_submodule_logging()

        # 清理过期缓存
        self._cleanup_expired_cache()

    def _cleanup_expired_cache(self):
        """清理过期的缓存条目"""
        try:
            self.brain_cache.clear_expired()
            self.clickhouse_cache.clear_expired()
            self.logger.info("缓存清理完成")
        except Exception as e:
            self.logger.warning(f"缓存清理失败: {e}")

    def get_cache_stats(self) -> Dict[str, CacheStats]:
        """获取缓存统计信息"""
        return {
            'brain_api': self.brain_cache.stats,
            'clickhouse': self.clickhouse_cache.stats
        }

    def clear_all_cache(self):
        """清空所有缓存"""
        self.brain_cache.clear_all()
        self.clickhouse_cache.clear_all()
        self.logger.info("所有缓存已清空")

    def prepare_directories(self):
        """准备输出目录"""
        for directory in [
            self.config.output_dir,
            self.config.temp_dir,
            self.config.log_dir,
        ]:
            directory.mkdir(parents=True, exist_ok=True)

    def get_file_paths(self) -> Dict[str, str]:
        """获取各步骤的文件路径"""
        return {
            "step1_errors": str(self.config.temp_dir / "conversion_errors.log"),
            "step1_warnings": str(self.config.temp_dir / "conversion_warnings.log"),
            "step1_stats": str(self.config.temp_dir / "conversion_stats.log"),
            "step2_errors": str(self.config.temp_dir / "brain_errors.csv"),
            "step3_errors": str(self.config.temp_dir / "clickhouse_errors.csv"),
            "final_csv": str(self.config.output_dir / "final_processed.csv"),
            "duplicates_log": str(self.config.output_dir / "duplicates.log"),
            "warnings_csv": str(self.config.output_dir / "warnings.csv"),
            "temp_step1_csv": str(self.config.temp_dir / "material_items.csv"),
            "temp_step2_csv": str(
                self.config.temp_dir / "material_items_brain_updated.csv"
            ),
            "temp_step3_csv": str(
                self.config.temp_dir / "material_items_clickhouse_updated.csv"
            ),
            "pipeline_log": str(self.config.log_dir / "modular_pipeline.log"),
            "full_execution_log": str(self.config.log_dir / "full_execution.log"),
        }

    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger("modular_pipeline")
        logger.setLevel(getattr(logging, self.config.log_level))

        # 清除现有的处理器以避免重复
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(self.config.log_format)
        
        # 控制台处理器 - 显示原始脚本的日志输出
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(getattr(logging, self.config.log_level))
        logger.addHandler(console_handler)
        
        # 确保日志目录存在
        self.config.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 文件处理器 - 保存所有运行日志到特定文件
        log_file_path = self.config.log_dir / "modular_pipeline.log"
        file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)  # 文件中记录所有级别的日志
        logger.addHandler(file_handler)
        
        # 完整执行日志处理器 - 包含详细的执行信息
        full_log_path = self.config.log_dir / "full_execution.log"
        full_handler = logging.FileHandler(full_log_path, mode='w', encoding='utf-8')
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        full_handler.setFormatter(detailed_formatter)
        full_handler.setLevel(logging.DEBUG)
        logger.addHandler(full_handler)
        
        # 记录日志设置信息
        logger.info(f"日志系统已初始化")
        logger.info(f"控制台日志级别: {self.config.log_level}")
        logger.info(f"文件日志保存到: {log_file_path}")
        logger.info(f"详细执行日志保存到: {full_log_path}")
        
        return logger
    
    def _setup_submodule_logging(self):
        """配置子模块的日志记录器"""
        # 获取子模块的日志记录器并配置它们
        submodule_loggers = [
            'convert_jsonl_to_csv',
            'update_csv_with_brain_api', 
            'update_csv_with_clickhouse',
            'deduplicate_and_update_prices'
        ]
        
        for logger_name in submodule_loggers:
            sublogger = logging.getLogger(logger_name)
            sublogger.setLevel(logging.DEBUG)
            
            # 清除现有处理器
            sublogger.handlers.clear()
            
            # 确保日志目录存在
            self.config.log_dir.mkdir(parents=True, exist_ok=True)
            
            # 添加文件处理器，将子模块日志也保存到主日志文件
            log_file_path = self.config.log_dir / "modular_pipeline.log"
            file_handler = logging.FileHandler(log_file_path, mode='a', encoding='utf-8')
            formatter = logging.Formatter(f'%(asctime)s - {logger_name} - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            sublogger.addHandler(file_handler)
            
            # 添加详细日志处理器
            full_log_path = self.config.log_dir / "full_execution.log"
            full_handler = logging.FileHandler(full_log_path, mode='a', encoding='utf-8')
            detailed_formatter = logging.Formatter(
                f'%(asctime)s - {logger_name} - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            full_handler.setFormatter(detailed_formatter)
            sublogger.addHandler(full_handler)
            
            # 设置传播，这样子模块的日志也会传播到根日志记录器
            sublogger.propagate = True
            
        self.logger.info("子模块日志记录器配置完成")
    
    def _log_system_info(self):
        """记录系统信息和配置"""
        import platform
        from datetime import datetime
        
        self.logger.info("=" * 80)
        self.logger.info("模块化数据处理流水线启动")
        self.logger.info("=" * 80)
        self.logger.info(f"启动时间: {datetime.now()}")
        self.logger.info(f"Python版本: {platform.python_version()}")
        self.logger.info(f"操作系统: {platform.system()} {platform.release()}")
        self.logger.info(f"处理器: {platform.processor()}")
        
        # 记录内存信息
        process = psutil.Process()
        memory_info = process.memory_info()
        self.logger.info(f"当前内存使用: {memory_info.rss / 1024 / 1024:.1f} MB")
        
        # 记录配置信息
        self.logger.info("\n配置信息:")
        self.logger.info(f"  输入文件: {self.config.input_file}")
        self.logger.info(f"  输出目录: {self.config.output_dir}")
        self.logger.info(f"  临时目录: {self.config.temp_dir}")
        self.logger.info(f"  日志目录: {self.config.log_dir}")
        self.logger.info(f"  日志级别: {self.config.log_level}")
        self.logger.info(f"  Brain API批处理大小: {self.config.brain_api_batch_size}")
        self.logger.info(f"  ClickHouse主机: {self.config.clickhouse_host}")
        self.logger.info(f"  清理临时文件: {self.config.cleanup_temp}")
        self.logger.info(f"  步骤超时时间: {self.config.step_timeout}秒")
        self.logger.info("=" * 80)
    
    def _capture_and_log_output(self, func, *args, **kwargs):
        """捕获函数输出并记录到日志"""
        # 重置缓冲区
        self.output_buffer.seek(0)
        self.output_buffer.truncate(0)
        self.error_buffer.seek(0)
        self.error_buffer.truncate(0)
        
        try:
            # 捕获标准输出和标准错误
            with redirect_stdout(self.output_buffer), redirect_stderr(self.error_buffer):
                result = func(*args, **kwargs)
            
            # 获取捕获的输出
            stdout_content = self.output_buffer.getvalue()
            stderr_content = self.error_buffer.getvalue()
            
            # 将捕获的输出记录到日志并显示到控制台
            if stdout_content:
                for line in stdout_content.strip().split('\n'):
                    if line.strip():
                        print(line)  # 显示原始输出
                        self.logger.info(f"[子模块输出] {line}")
            
            if stderr_content:
                for line in stderr_content.strip().split('\n'):
                    if line.strip():
                        print(line, file=sys.stderr)  # 显示原始错误输出
                        self.logger.warning(f"[子模块错误] {line}")
            
            return result
            
        except Exception as e:
            # 记录异常信息
            self.logger.error(f"函数执行异常: {e}")
            # 获取可能的输出
            stdout_content = self.output_buffer.getvalue()
            stderr_content = self.error_buffer.getvalue()
            
            if stdout_content:
                self.logger.info(f"异常前的输出: {stdout_content}")
            if stderr_content:
                self.logger.error(f"异常前的错误: {stderr_content}")
            
            raise

    async def run_pipeline(
        self, input_jsonl_file: str
    ) -> Tuple[pd.DataFrame, Dict[str, List]]:
        """运行完整的模块化流水线

        Args:
            input_jsonl_file: 输入的JSONL文件路径

        Returns:
            Tuple[pd.DataFrame, Dict[str, List]]: (最终处理的DataFrame, 各步骤的错误记录)
        """
        self.monitor.start_pipeline()
        
        # 记录系统信息和配置
        self._log_system_info()
        
        self.logger.info(f"开始模块化流水线处理: {input_jsonl_file}")
        
        # 检查输入文件
        from pathlib import Path
        input_path = Path(input_jsonl_file)
        if not input_path.exists():
            self.logger.error(f"输入文件不存在: {input_jsonl_file}")
            raise FileNotFoundError(f"输入文件不存在: {input_jsonl_file}")
        
        file_size_mb = input_path.stat().st_size / 1024 / 1024
        self.logger.info(f"输入文件大小: {file_size_mb:.1f} MB")

        # 获取文件路径
        paths = self.get_file_paths()

        all_errors = {
            "jsonl_conversion": [],
            "brain_api_update": [],
            "clickhouse_update": [],
            "deduplication": [],
        }

        try:
            # 步骤1: JSONL转换为DataFrame
            self.monitor.start_step("jsonl_conversion")
            self.logger.info("步骤1: 转换JSONL文件为DataFrame")
            self.logger.info(f"正在处理文件: {input_jsonl_file}")

            success_df, error_records, warning_records = self._capture_and_log_output(
                self.jsonl_converter.convert_jsonl_to_dataframe, input_jsonl_file, str(self.config.temp_dir)
            )
            all_errors["jsonl_conversion"].extend(error_records)
            all_errors["jsonl_conversion"].extend(warning_records)

            # 生成详细的统计报告
            self.logger.info("生成JSONL转换统计报告...")
            self._generate_conversion_stats_report(
                success_df, error_records, warning_records, paths["step1_stats"]
            )
            self.logger.info("统计报告生成完成")

            # 保存步骤1的中间结果和错误日志
            if not success_df.empty:
                self.logger.info("保存步骤1中间结果...")
                success_df.to_csv(paths["temp_step1_csv"], index=False)
                self.logger.info(f"步骤1中间结果已保存到: {paths['temp_step1_csv']}")

            self.logger.info("保存步骤1错误和警告日志...")
            self._save_step_errors(
                error_records, paths["step1_errors"], "conversion_errors"
            )
            self._save_step_errors(
                warning_records, paths["step1_warnings"], "conversion_warnings"
            )
            self.logger.info("步骤1日志保存完成")

            self.monitor.end_step(
                "jsonl_conversion",
                len(success_df),
                len(error_records) + len(warning_records),
            )
            self.logger.info(
                f"JSONL转换完成: 成功 {len(success_df)} 行, 错误/警告 {len(error_records) + len(warning_records)} 条"
            )

            if success_df.empty:
                self.logger.warning("JSONL转换后没有有效数据，流水线终止")
                return success_df, all_errors

            # 步骤2: 优化的Brain API更新SMILES
            self.monitor.start_step("brain_api_update")
            self.logger.info("步骤2: 使用优化的Brain API更新SMILES")
            self.logger.info(f"待更新数据行数: {len(success_df)}")

            # 使用优化的处理器（支持去重、缓存和并发）
            self.logger.info("开始优化的Brain API更新...")
            updated_df, brain_errors = await self.optimized_brain_processor.process_dataframe_optimized(
                success_df
            )
            self.logger.info("优化的Brain API更新完成")
            all_errors["brain_api_update"].extend(brain_errors)

            # 记录缓存统计
            brain_cache_stats = self.brain_cache.stats
            self.logger.info(f"Brain API缓存统计: 命中率 {brain_cache_stats.hit_ratio:.1%}, "
                           f"总请求 {brain_cache_stats.total_requests}, "
                           f"缓存大小 {brain_cache_stats.cache_size}")

            # 保存步骤2的中间结果和错误日志
            if not updated_df.empty:
                self.logger.info("保存步骤2中间结果...")
                updated_df.to_csv(paths["temp_step2_csv"], index=False)
                self.logger.info(f"步骤2中间结果已保存到: {paths['temp_step2_csv']}")

            self.logger.info("保存步骤2错误日志...")
            self._save_step_errors(brain_errors, paths["step2_errors"], "brain_errors")
            self.logger.info("步骤2日志保存完成")

            self.monitor.end_step(
                "brain_api_update", len(updated_df), len(brain_errors)
            )
            self.logger.info(
                f"Brain API更新完成: 成功 {len(updated_df)} 行, 错误 {len(brain_errors)} 条"
            )

            # 步骤3: 优化的ClickHouse更新hazard信息
            self.monitor.start_step("clickhouse_update")
            self.logger.info("步骤3: 使用优化的ClickHouse更新hazard信息")
            self.logger.info(f"待更新数据行数: {len(updated_df)}")

            # 使用优化的处理器（支持去重、缓存和批处理）
            self.logger.info("开始优化的ClickHouse更新...")
            hazard_updated_df, clickhouse_errors = self.optimized_clickhouse_processor.process_dataframe_optimized(
                updated_df
            )
            self.logger.info("优化的ClickHouse更新完成")
            all_errors["clickhouse_update"].extend(clickhouse_errors)

            # 记录缓存统计
            clickhouse_cache_stats = self.clickhouse_cache.stats
            self.logger.info(f"ClickHouse缓存统计: 命中率 {clickhouse_cache_stats.hit_ratio:.1%}, "
                           f"总请求 {clickhouse_cache_stats.total_requests}, "
                           f"缓存大小 {clickhouse_cache_stats.cache_size}")

            # 保存步骤3的中间结果和错误日志
            if not hazard_updated_df.empty:
                self.logger.info("保存步骤3中间结果...")
                hazard_updated_df.to_csv(paths["temp_step3_csv"], index=False)
                self.logger.info(f"步骤3中间结果已保存到: {paths['temp_step3_csv']}")

            self.logger.info("保存步骤3错误日志...")
            self._save_step_errors(
                clickhouse_errors, paths["step3_errors"], "clickhouse_errors"
            )
            self.logger.info("步骤3日志保存完成")

            self.monitor.end_step(
                "clickhouse_update", len(hazard_updated_df), len(clickhouse_errors)
            )
            self.logger.info(
                f"ClickHouse更新完成: 成功 {len(hazard_updated_df)} 行, 错误 {len(clickhouse_errors)} 条"
            )

            # 步骤4: 去重和价格更新
            self.monitor.start_step("deduplication")
            self.logger.info("步骤4: 去重和价格更新")
            self.logger.info(f"去重前数据行数: {len(hazard_updated_df)}")

            final_df, dedup_records = self._capture_and_log_output(
                deduplicate_and_update_dataframe, hazard_updated_df
            )
            all_errors["deduplication"].extend(dedup_records)

            # 保存最终结果
            if not final_df.empty:
                self.logger.info("保存最终处理结果...")
                final_df.to_csv(paths["final_csv"], index=False)
                self.logger.info(f"最终结果已保存到: {paths['final_csv']}")

            # 保存去重日志
            self.logger.info("保存去重日志...")
            self._save_step_errors(dedup_records, paths["duplicates_log"], "duplicates")
            self.logger.info("步骤4日志保存完成")

            self.monitor.end_step("deduplication", len(final_df), len(dedup_records))
            self.logger.info(
                f"去重和价格更新完成: 最终 {len(final_df)} 行, 记录 {len(dedup_records)} 条"
            )

            return final_df, all_errors

        except Exception as e:
            self.logger.error(f"流水线执行出错: {e}")
            self.logger.exception("详细错误信息:")
            raise
        finally:
            self.monitor.end_pipeline()
            self.monitor.print_summary()
            self.print_final_summary(paths)
            self._print_cache_summary()

            # 记录最终统计信息
            self.logger.info("\n" + "=" * 80)
            self.logger.info("优化流水线执行完成")
            self.logger.info(f"总执行时间: {self.monitor.stats.total_duration:.2f} 秒")
            self.logger.info(f"最终内存使用: {self.monitor.stats.memory_usage.get('pipeline_end', 0):.1f} MB")
            self.logger.info("所有日志已保存到文件")
            self.logger.info("=" * 80)

    def _generate_conversion_stats_report(self, success_df, error_records, warning_records, stats_file_path: str):
        """生成JSONL转换的详细统计报告"""
        try:
            from datetime import datetime
            
            # 计算基本统计
            # 注意：total_lines应该是原始输入文件的实际行数
            # 这里我们需要从转换器获取实际处理的行数
            if hasattr(self.jsonl_converter, 'total_processed_lines'):
                total_lines = self.jsonl_converter.total_processed_lines
            else:
                # 回退方案：成功行数 + 错误记录数（这可能不准确）
                total_lines = len(success_df) + len(error_records)
            
            success_count = len(success_df)
            error_count = len(error_records)
            warning_count = len(warning_records)
            success_rate = (success_count / total_lines * 100) if total_lines > 0 else 0

            # 统计错误类型
            error_stats = {}
            for error_record in error_records:
                errors = error_record.get('errors', [])
                for error in errors:
                    error_type = error.split(':')[0] if ':' in error else error
                    error_stats[error_type] = error_stats.get(error_type, 0) + 1

            # 统计警告类型
            warning_stats = {}
            for warning_record in warning_records:
                warnings = warning_record.get('warnings', [])
                for warning in warnings:
                    warning_type = warning.split(':')[0] if ':' in warning else warning
                    warning_stats[warning_type] = warning_stats.get(warning_type, 0) + 1

            # 生成统计报告
            stats_report = []
            stats_report.append(f"\n✅ 转换完成!")
            stats_report.append(f"📊 统计信息:")
            stats_report.append(f"  - 总处理行数: {total_lines:,}")
            stats_report.append(f"  - 成功转换: {success_count:,}")
            stats_report.append(f"  - 错误行数: {error_count:,}")
            stats_report.append(f"  - 警告行数: {warning_count:,}")
            stats_report.append(f"  - 成功率: {success_rate:.2f}%")

            # 错误统计
            if error_stats:
                stats_report.append(f"\n❌ 错误类型统计:")
                for error_type, count in sorted(error_stats.items(), key=lambda x: x[1], reverse=True):
                    stats_report.append(f"  - {error_type}: {count:,} 次")
            
            # 详细错误信息（显示前10个错误记录）
            if error_records:
                stats_report.append(f"\n📋 错误详情 (前10条):")
                for i, error_record in enumerate(error_records[:10]):
                    line_num = error_record.get('line_number', '未知')
                    errors = error_record.get('errors', [])
                    if isinstance(errors, list):
                        error_msg = '; '.join(errors[:2])  # 只显示前2个错误
                    else:
                        error_msg = str(errors)[:100]  # 限制长度
                    stats_report.append(f"  {i+1}. 第{line_num}行: {error_msg}")
                
                if len(error_records) > 10:
                    stats_report.append(f"  ... 还有 {len(error_records) - 10} 条错误记录")

            # 警告统计
            if warning_stats:
                stats_report.append(f"\n⚠️ 警告类型统计:")
                for warning_type, count in sorted(warning_stats.items(), key=lambda x: x[1], reverse=True):
                    stats_report.append(f"  - {warning_type}: {count:,} 次")

            # 获取转换器的统计信息（如果有的话）
            if hasattr(self.jsonl_converter, 'unknown_units') and self.jsonl_converter.unknown_units:
                stats_report.append(f"\n🔍 发现的未知单位:")
                for unit in sorted(self.jsonl_converter.unknown_units):
                    stats_report.append(f"  - {unit}")
                stats_report.append(f"\n💡 提示: 发现 {len(self.jsonl_converter.unknown_units)} 种未知单位，这些单位保持原样未进行转换。")

            if hasattr(self.jsonl_converter, 'parsed_specifications_success') and self.jsonl_converter.parsed_specifications_success:
                stats_report.append(f"\n✅ 成功解析的specification ({len(self.jsonl_converter.parsed_specifications_success)} 个):")
                for spec in sorted(self.jsonl_converter.parsed_specifications_success):
                    stats_report.append(f"  - {spec}")

            if hasattr(self.jsonl_converter, 'parsed_specifications_failed') and self.jsonl_converter.parsed_specifications_failed:
                total_failed_count = sum(self.jsonl_converter.parsed_specifications_failed_count.values())
                stats_report.append(f"\n❌ 解析失败的specification统计:")
                stats_report.append(f"  - 解析失败的specification种类数: {len(self.jsonl_converter.parsed_specifications_failed)} 个")
                stats_report.append(f"  - 解析失败的总次数: {total_failed_count:,} 次")
                stats_report.append(f"  - 平均每个specification失败次数: {total_failed_count/len(self.jsonl_converter.parsed_specifications_failed):.1f} 次")
                stats_report.append(f"\n❌ 解析失败的specification详情 ({len(self.jsonl_converter.parsed_specifications_failed)} 个):")
                sorted_failed = sorted(self.jsonl_converter.parsed_specifications_failed_count.items(), key=lambda x: x[1], reverse=True)
                for spec, count in sorted_failed:
                    if len(spec) > 80:
                        stats_report.append(f"  - {spec[:80]}... (失败 {count:,} 次)")
                    else:
                        stats_report.append(f"  - {spec} (失败 {count:,} 次)")

            # 输出到控制台
            for line in stats_report:
                print(line)

            # 写入统计信息到日志文件
            with open(stats_file_path, 'w', encoding='utf-8') as statsfile:
                statsfile.write(f"转换统计报告 - 生成时间: {datetime.now()}\n")
                statsfile.write("=" * 80 + "\n")
                for line in stats_report:
                    statsfile.write(line + "\n")

            self.logger.info(f"📄 统计报告已保存到: {stats_file_path}")

        except Exception as e:
            self.logger.error(f"生成统计报告失败: {e}")

    def _save_step_errors(self, errors: List, file_path: str, error_type: str):
        """保存步骤错误到文件"""
        if not errors:
            return

        try:
            import json
            from pathlib import Path
            from datetime import datetime

            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)

            if error_type in ["duplicates"]:
                # 对于去重日志，保存为文本格式
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(f"去重日志 - 生成时间: {datetime.now()}\n")
                    f.write("=" * 80 + "\n")
                    for error in errors:
                        f.write(f"重复记录: {error}\n")
            elif file_path.endswith(".csv"):
                # 对于CSV格式的错误文件
                if errors and isinstance(errors[0], dict):
                    import pandas as pd
                    pd.DataFrame(errors).to_csv(file_path, index=False)
                else:
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write("error\n")
                        for error in errors:
                            f.write(f"{error}\n")
            else:
                # 对于日志格式的错误文件
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(f"警告日志 - 生成时间: {datetime.now()}\n")
                    f.write("=" * 80 + "\n")
                    for error in errors:
                        if isinstance(error, dict):
                            f.write(f"行号: {error.get('line_number', 'N/A')}\n")
                            f.write(f"错误: {error.get('errors', error.get('warnings', 'N/A'))}\n")
                            f.write("-" * 40 + "\n")
                        else:
                            f.write(f"{error}\n")

            self.logger.info(f"错误日志已保存到: {file_path}")

        except Exception as e:
            self.logger.error(f"保存{error_type}失败: {e}")

    def print_final_summary(self, paths: Dict[str, str]):
        """打印最终摘要"""
        from pathlib import Path

        summary_lines = []
        summary_lines.append("\n📋 处理完成摘要:")
        summary_lines.append(f"  📁 输出目录: {self.config.output_dir}")
        summary_lines.append(f"  📁 临时目录: {self.config.temp_dir}")
        summary_lines.append(f"  📁 日志目录: {self.config.log_dir}")
        summary_lines.append(f"  📄 最终数据文件: {paths['final_csv']}")
        summary_lines.append(f"  📝 重复记录日志: {paths['duplicates_log']}")

        # 统计文件大小
        final_csv_path = Path(paths["final_csv"])
        if final_csv_path.exists():
            size_mb = final_csv_path.stat().st_size / 1024 / 1024
            summary_lines.append(f"  📊 最终文件大小: {size_mb:.1f} MB")

        # 日志文件信息
        summary_lines.append("\n📝 日志文件:")
        log_files = [
            (paths["pipeline_log"], "主要运行日志"),
            (paths["full_execution_log"], "详细执行日志"),
        ]
        
        for log_file, description in log_files:
            log_path = Path(log_file)
            if log_path.exists():
                size_kb = log_path.stat().st_size / 1024
                summary_lines.append(f"  📄 {description}: {log_file} ({size_kb:.1f} KB)")

        # 统计错误文件
        error_files = [
            (paths["step1_errors"], "步骤1转换错误"),
            (paths["step1_warnings"], "步骤1转换警告"),
            (paths["step2_errors"], "步骤2 Brain API错误"),
            (paths["step3_errors"], "步骤3 ClickHouse错误"),
        ]

        has_errors = False
        for error_file, description in error_files:
            error_path = Path(error_file)
            if error_path.exists() and error_path.stat().st_size > 0:
                if not has_errors:
                    summary_lines.append("\n❌ 错误文件:")
                    has_errors = True
                summary_lines.append(f"  📄 {description}: {error_file}")

        # 统计临时文件
        temp_files = [
            (paths["temp_step1_csv"], "步骤1中间结果"),
            (paths["temp_step2_csv"], "步骤2中间结果"),
            (paths["temp_step3_csv"], "步骤3中间结果"),
        ]

        summary_lines.append("\n📂 临时文件:")
        for temp_file, description in temp_files:
            temp_path = Path(temp_file)
            if temp_path.exists():
                size_mb = temp_path.stat().st_size / 1024 / 1024
                summary_lines.append(f"  📄 {description}: {temp_file} ({size_mb:.1f} MB)")
        
        # 输出到控制台和日志
        for line in summary_lines:
            print(line)
            self.logger.info(line.strip())

    def _print_cache_summary(self):
        """打印缓存统计摘要"""
        cache_stats = self.get_cache_stats()

        print("\n📊 缓存性能统计:")
        print("=" * 60)

        for cache_name, stats in cache_stats.items():
            print(f"\n{cache_name.upper()} 缓存:")
            print(f"  总请求数: {stats.total_requests:,}")
            print(f"  缓存命中: {stats.cache_hits:,}")
            print(f"  缓存未命中: {stats.cache_misses:,}")
            print(f"  命中率: {stats.hit_ratio:.1%}")
            print(f"  缓存大小: {stats.cache_size:,} 条记录")
            if stats.last_updated:
                print(f"  最后更新: {stats.last_updated.strftime('%Y-%m-%d %H:%M:%S')}")

        print("=" * 60)

    def cleanup_temp_files(self):
        """清理临时文件"""
        from pathlib import Path

        paths = self.get_file_paths()
        temp_files = [
            paths["temp_step1_csv"],
            paths["temp_step2_csv"],
            paths["temp_step3_csv"],
        ]

        for temp_file in temp_files:
            temp_path = Path(temp_file)
            if temp_path.exists():
                temp_path.unlink()
                print(f"🗑️ 已删除临时文件: {temp_file}")
                self.logger.info(f"已删除临时文件: {temp_file}")

    async def save_results(
        self, df: pd.DataFrame, errors: Dict[str, List], output_dir: str
    ):
        """保存处理结果

        Args:
            df: 最终处理的DataFrame
            errors: 各步骤的错误记录
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 保存最终结果
        output_file = os.path.join(output_dir, "final_processed_data.csv")
        df.to_csv(output_file, index=False)
        self.logger.info(f"最终结果已保存到: {output_file}")

        # 保存各步骤的错误记录
        for step, error_list in errors.items():
            if error_list:
                error_file = os.path.join(output_dir, f"{step}_errors.json")
                import json

                with open(error_file, "w", encoding="utf-8") as f:
                    json.dump(error_list, f, ensure_ascii=False, indent=2)
                self.logger.info(f"{step}错误记录已保存到: {error_file}")


async def main():
    """主函数"""
    import argparse
    from pathlib import Path

    parser = argparse.ArgumentParser(
        description="模块化数据处理流水线",
        formatter_class=argparse.RawTextHelpFormatter,
    )

    parser.add_argument("input_file", help="输入JSONL文件路径")

    parser.add_argument(
        "--output-dir", default="./output", help="输出目录 (默认: ./output)"
    )

    parser.add_argument(
        "--temp-dir", default="./temp", help="临时文件目录 (默认: ./temp)"
    )

    parser.add_argument("--config-file", help="配置文件路径 (可选)")

    parser.add_argument(
        "--brain-api-batch-size",
        type=int,
        default=10000,
        help="Brain API批处理大小 (默认: 10000)",
    )

    parser.add_argument(
        "--clickhouse-host",
        default="localhost",
        help="ClickHouse主机地址 (默认: localhost)",
    )

    parser.add_argument(
        "--cleanup-temp", action="store_true", help="完成后清理临时文件"
    )

    parser.add_argument(
        "--step-timeout", type=int, default=3600, help="单步骤超时时间(秒) (默认: 3600)"
    )

    # 新增优化相关参数
    parser.add_argument(
        "--cache-dir", help="缓存目录 (默认: temp_dir/cache)"
    )

    parser.add_argument(
        "--cache-ttl-hours", type=int, default=24, help="缓存TTL小时数 (默认: 24)"
    )

    parser.add_argument(
        "--brain-api-max-concurrent", type=int, default=3,
        help="Brain API最大并发请求数 (默认: 3)"
    )

    parser.add_argument(
        "--clickhouse-batch-size", type=int, default=10000,
        help="ClickHouse批处理大小 (默认: 10000)"
    )

    parser.add_argument(
        "--clear-cache", action="store_true", help="启动前清空所有缓存"
    )

    args = parser.parse_args()

    # 先创建基础配置
    config = PipelineConfig(input_file=Path(args.input_file))

    # 如果提供了配置文件，先加载配置文件
    if args.config_file:
        config.load_from_file(args.config_file)

    # 然后用命令行参数覆盖配置（只覆盖非默认值）
    if args.output_dir != "./output":
        config.output_dir = Path(args.output_dir)
    if args.temp_dir != "./temp":
        config.temp_dir = Path(args.temp_dir)
    if args.brain_api_batch_size != 10000:
        config.brain_api_batch_size = args.brain_api_batch_size
    if args.clickhouse_host != "localhost":
        config.clickhouse_host = args.clickhouse_host
    if args.cleanup_temp:
        config.cleanup_temp = args.cleanup_temp
    if args.step_timeout != 3600:
        config.step_timeout = args.step_timeout

    # 新增优化参数
    if args.cache_dir:
        config.cache_dir = Path(args.cache_dir)
    else:
        config.cache_dir = config.temp_dir / 'cache'

    config.cache_ttl_hours = args.cache_ttl_hours
    config.brain_api_max_concurrent = args.brain_api_max_concurrent
    config.clickhouse_batch_size = args.clickhouse_batch_size

    # 验证配置
    print("🔍 验证配置...")
    if not config.validate():
        print("❌ 配置验证失败，流水线终止")
        return 1
    print("✅ 配置验证通过")

    # 创建并运行优化流水线
    print("🚀 初始化优化的模块化数据处理流水线...")
    pipeline = OptimizedModularPipeline(config)

    # 清空缓存（如果请求）
    if args.clear_cache:
        print("🗑️ 清空所有缓存...")
        pipeline.clear_all_cache()
        print("✅ 缓存已清空")

    print("📂 日志文件位置:")
    print(f"  主要日志: {config.log_dir / 'modular_pipeline.log'}")
    print(f"  详细日志: {config.log_dir / 'full_execution.log'}")
    print("\n🔄 开始执行流水线...")

    try:
        final_df, errors = await pipeline.run_pipeline(args.input_file)

        # 清理临时文件（如果配置允许）
        if config.cleanup_temp:
            print("🧹 清理临时文件...")
            pipeline.cleanup_temp_files()

        print(f"\n🎉 流水线执行完成! 最终处理了 {len(final_df)} 行数据")
        print(f"📁 结果已保存到: {config.output_dir}")
        print(f"📝 详细日志已保存到: {config.log_dir}")

    except Exception as e:
        print(f"💥 流水线执行失败: {e}")
        # 确保错误也记录到日志中
        if 'pipeline' in locals():
            pipeline.logger.error(f"流水线执行失败: {e}")
            pipeline.logger.exception("详细错误信息:")
        return 1

    return 0


if __name__ == "__main__":
    exit(asyncio.run(main()))
