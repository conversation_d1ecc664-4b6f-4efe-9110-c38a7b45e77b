#!/usr/bin/env python3
"""
Brain API Error Analysis Tool

Analyzes brain_errors.csv files to identify patterns and root causes
of "No API result found" errors and other Brain API processing issues.
"""

import pandas as pd
import sys
import os
from pathlib import Path
from collections import Counter, defaultdict
import argparse
import re


class BrainAPIErrorAnalyzer:
    """Analyzer for Brain API errors"""
    
    def __init__(self, error_file: str):
        self.error_file = Path(error_file)
        self.df = None
        self.analysis_results = {}
        
    def load_error_data(self):
        """Load and validate error data"""
        if not self.error_file.exists():
            raise FileNotFoundError(f"Error file not found: {self.error_file}")
        
        try:
            self.df = pd.read_csv(self.error_file)
            print(f"✅ Loaded {len(self.df)} error records from {self.error_file}")
            
            # Display basic info
            print(f"📊 Columns: {list(self.df.columns)}")
            if 'error_reason' in self.df.columns:
                print(f"🔍 Error reasons found: {self.df['error_reason'].nunique()} unique types")
            
        except Exception as e:
            raise Exception(f"Failed to load error file: {e}")
    
    def analyze_error_patterns(self):
        """Analyze error patterns and categorize them"""
        if self.df is None:
            raise ValueError("No data loaded. Call load_error_data() first.")
        
        results = {}
        
        # 1. Error reason analysis
        if 'error_reason' in self.df.columns:
            error_counts = self.df['error_reason'].value_counts()
            results['error_reasons'] = error_counts.to_dict()
            
            print("\n📈 Top Error Reasons:")
            print("=" * 60)
            for reason, count in error_counts.head(10).items():
                percentage = (count / len(self.df)) * 100
                print(f"  {reason}: {count:,} ({percentage:.1f}%)")
        
        # 2. SMILES pattern analysis
        if 'canonical_smiles' in self.df.columns:
            smiles_analysis = self._analyze_smiles_patterns()
            results['smiles_patterns'] = smiles_analysis
        
        # 3. Error categorization
        error_categories = self._categorize_errors()
        results['error_categories'] = error_categories
        
        # 4. Data quality issues
        data_quality = self._analyze_data_quality()
        results['data_quality'] = data_quality
        
        self.analysis_results = results
        return results
    
    def _analyze_smiles_patterns(self):
        """Analyze SMILES patterns in error records"""
        smiles_col = self.df['canonical_smiles']
        
        patterns = {
            'empty_smiles': smiles_col.isna().sum() + (smiles_col == '').sum(),
            'very_short': (smiles_col.str.len() < 3).sum(),
            'very_long': (smiles_col.str.len() > 500).sum(),
            'contains_invalid_chars': 0,
            'unusual_patterns': 0
        }
        
        # Check for invalid characters
        invalid_chars = ['<', '>', '{', '}', '|', '\\', '"', "'"]
        for char in invalid_chars:
            patterns['contains_invalid_chars'] += smiles_col.str.contains(char, na=False).sum()
        
        # Check for unusual patterns
        unusual_patterns = [
            r'\.{3,}',  # Multiple dots
            r'\s{2,}',  # Multiple spaces
            r'[A-Z]{10,}',  # Long sequences of capitals
        ]
        
        for pattern in unusual_patterns:
            patterns['unusual_patterns'] += smiles_col.str.contains(pattern, na=False).sum()
        
        print("\n🧪 SMILES Pattern Analysis:")
        print("=" * 60)
        for pattern, count in patterns.items():
            if count > 0:
                percentage = (count / len(self.df)) * 100
                print(f"  {pattern}: {count:,} ({percentage:.1f}%)")
        
        return patterns
    
    def _categorize_errors(self):
        """Categorize errors into meaningful groups"""
        if 'error_reason' not in self.df.columns:
            return {}
        
        categories = defaultdict(int)
        
        for error_reason in self.df['error_reason']:
            if pd.isna(error_reason):
                categories['unknown'] += 1
                continue
                
            error_lower = str(error_reason).lower()
            
            if 'no api result found' in error_lower or 'no result' in error_lower:
                categories['missing_api_result'] += 1
            elif 'invalid' in error_lower or 'malformed' in error_lower:
                categories['invalid_smiles'] += 1
            elif 'timeout' in error_lower:
                categories['timeout'] += 1
            elif 'network' in error_lower or 'connection' in error_lower:
                categories['network_error'] += 1
            elif 'api' in error_lower and 'fail' in error_lower:
                categories['api_failure'] += 1
            elif 'empty' in error_lower or 'null' in error_lower:
                categories['empty_data'] += 1
            else:
                categories['other'] += 1
        
        print("\n📊 Error Categories:")
        print("=" * 60)
        total_errors = sum(categories.values())
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_errors) * 100
            print(f"  {category}: {count:,} ({percentage:.1f}%)")
        
        return dict(categories)
    
    def _analyze_data_quality(self):
        """Analyze data quality issues"""
        quality_issues = {}
        
        # Check for duplicate SMILES
        if 'canonical_smiles' in self.df.columns:
            duplicates = self.df['canonical_smiles'].duplicated().sum()
            quality_issues['duplicate_smiles'] = duplicates
        
        # Check for missing critical fields
        critical_fields = ['canonical_smiles']
        for field in critical_fields:
            if field in self.df.columns:
                missing = self.df[field].isna().sum()
                quality_issues[f'missing_{field}'] = missing
        
        print("\n🔍 Data Quality Issues:")
        print("=" * 60)
        for issue, count in quality_issues.items():
            if count > 0:
                percentage = (count / len(self.df)) * 100
                print(f"  {issue}: {count:,} ({percentage:.1f}%)")
        
        return quality_issues
    
    def generate_recommendations(self):
        """Generate specific recommendations based on analysis"""
        if not self.analysis_results:
            print("⚠️ No analysis results available. Run analyze_error_patterns() first.")
            return
        
        print("\n💡 Recommendations to Reduce Errors:")
        print("=" * 80)
        
        categories = self.analysis_results.get('error_categories', {})
        smiles_patterns = self.analysis_results.get('smiles_patterns', {})
        
        # Recommendations based on error categories
        if categories.get('missing_api_result', 0) > 0:
            print("🔧 For 'No API result found' errors:")
            print("   • Implement comprehensive result validation in batch processing")
            print("   • Add retry logic for missing results")
            print("   • Ensure all input SMILES are tracked through processing pipeline")
            print("   • Validate API response completeness before caching")
        
        if categories.get('invalid_smiles', 0) > 0:
            print("\n🧪 For invalid SMILES errors:")
            print("   • Add SMILES validation before API calls")
            print("   • Implement SMILES sanitization/normalization")
            print("   • Filter out obviously invalid SMILES patterns")
        
        if categories.get('timeout', 0) > 0:
            print("\n⏱️ For timeout errors:")
            print("   • Reduce batch sizes for API calls")
            print("   • Implement exponential backoff retry")
            print("   • Add timeout configuration options")
        
        if categories.get('network_error', 0) > 0:
            print("\n🌐 For network errors:")
            print("   • Implement robust retry mechanisms")
            print("   • Add network connectivity checks")
            print("   • Consider connection pooling")
        
        # Recommendations based on SMILES patterns
        if smiles_patterns.get('empty_smiles', 0) > 0:
            print("\n📝 For empty SMILES:")
            print("   • Add input validation to filter empty SMILES")
            print("   • Handle empty values gracefully in preprocessing")
        
        if smiles_patterns.get('contains_invalid_chars', 0) > 0:
            print("\n🚫 For invalid characters:")
            print("   • Implement character validation and sanitization")
            print("   • Add SMILES format validation")
        
        print("\n🚀 General Performance Improvements:")
        print("   • Implement persistent caching to avoid reprocessing")
        print("   • Add progress monitoring and detailed logging")
        print("   • Use batch processing with optimal sizes")
        print("   • Implement graceful error handling and recovery")
    
    def export_analysis(self, output_file: str = None):
        """Export analysis results to file"""
        if not output_file:
            output_file = self.error_file.parent / f"{self.error_file.stem}_analysis.txt"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("Brain API Error Analysis Report\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Source file: {self.error_file}\n")
            f.write(f"Total error records: {len(self.df)}\n\n")
            
            # Write analysis results
            for section, data in self.analysis_results.items():
                f.write(f"{section.upper()}:\n")
                f.write("-" * 30 + "\n")
                for key, value in data.items():
                    f.write(f"  {key}: {value}\n")
                f.write("\n")
        
        print(f"📄 Analysis exported to: {output_file}")


def main():
    parser = argparse.ArgumentParser(
        description="Analyze Brain API error patterns and provide recommendations"
    )
    parser.add_argument("error_file", help="Path to brain_errors.csv file")
    parser.add_argument("--export", help="Export analysis to file")
    parser.add_argument("--sample", type=int, help="Analyze only first N records")
    
    args = parser.parse_args()
    
    try:
        analyzer = BrainAPIErrorAnalyzer(args.error_file)
        analyzer.load_error_data()
        
        if args.sample:
            analyzer.df = analyzer.df.head(args.sample)
            print(f"🔬 Analyzing sample of {len(analyzer.df)} records")
        
        analyzer.analyze_error_patterns()
        analyzer.generate_recommendations()
        
        if args.export:
            analyzer.export_analysis(args.export)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
