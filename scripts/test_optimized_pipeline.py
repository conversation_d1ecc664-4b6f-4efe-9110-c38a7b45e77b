#!/usr/bin/env python3
"""
Test script for the optimized modular pipeline
Demonstrates the new caching and batch processing features
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the scripts directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline_config import PipelineConfig
from modular_pipeline import OptimizedModularPipeline


async def test_optimized_pipeline():
    """Test the optimized pipeline with a small dataset"""
    
    # Create test configuration
    config = PipelineConfig(
        input_file=Path("test_input.jsonl"),
        output_dir=Path("./test_output"),
        temp_dir=Path("./test_temp"),
        log_dir=Path("./test_logs"),
        brain_api_batch_size=100,  # Smaller batch for testing
        clickhouse_batch_size=100,
        cache_ttl_hours=1,  # Short TTL for testing
        brain_api_max_concurrent=2,  # Reduced concurrency for testing
    )
    
    print("🧪 Testing Optimized Modular Pipeline")
    print("=" * 50)
    
    # Initialize the optimized pipeline
    pipeline = OptimizedModularPipeline(config)
    
    # Display initial cache stats
    print("\n📊 Initial Cache Statistics:")
    cache_stats = pipeline.get_cache_stats()
    for cache_name, stats in cache_stats.items():
        print(f"  {cache_name}: {stats.cache_size} entries, {stats.hit_ratio:.1%} hit rate")
    
    # Test cache functionality
    print("\n🔧 Testing Cache Functionality:")
    
    # Test Brain API cache
    test_smiles = ["CCO", "CC(=O)O", "C1=CC=CC=C1"]
    print(f"Testing with SMILES: {test_smiles}")
    
    # Simulate cache operations
    for smiles in test_smiles:
        pipeline.brain_cache.set(smiles, {
            'canonical_smiles': smiles,
            'inchified_smiles': f"InChI=1S/{smiles}",
            'success': True
        })
    
    # Test cache retrieval
    cached_results = pipeline.brain_cache.get_multiple(test_smiles)
    print(f"Cached results retrieved: {len(cached_results)} items")
    
    # Test missing keys detection
    all_test_smiles = test_smiles + ["INVALID_SMILES"]
    missing_keys = pipeline.brain_cache.get_missing_keys(all_test_smiles)
    print(f"Missing keys: {missing_keys}")
    
    # Display updated cache stats
    print("\n📊 Updated Cache Statistics:")
    cache_stats = pipeline.get_cache_stats()
    for cache_name, stats in cache_stats.items():
        print(f"  {cache_name}: {stats.cache_size} entries, "
              f"{stats.total_requests} requests, {stats.hit_ratio:.1%} hit rate")
    
    # Test cache persistence
    print("\n💾 Testing Cache Persistence:")
    pipeline.brain_cache.save()
    print("Cache saved to disk")
    
    # Test cache clearing
    print("\n🗑️ Testing Cache Clearing:")
    pipeline.clear_all_cache()
    cache_stats = pipeline.get_cache_stats()
    for cache_name, stats in cache_stats.items():
        print(f"  {cache_name}: {stats.cache_size} entries (should be 0)")
    
    print("\n✅ Optimized Pipeline Test Completed!")
    print("=" * 50)
    
    print("\n📋 Key Optimizations Implemented:")
    print("  ✓ SMILES deduplication before API calls")
    print("  ✓ Persistent local caching with TTL")
    print("  ✓ Concurrent batch processing for Brain API")
    print("  ✓ Optimized ClickHouse batch queries")
    print("  ✓ Cache hit/miss ratio monitoring")
    print("  ✓ Memory-efficient data processing")
    
    print("\n🚀 Performance Benefits:")
    print("  • Reduced API calls through deduplication")
    print("  • Faster processing with persistent caching")
    print("  • Improved throughput with concurrent processing")
    print("  • Better resource utilization")
    print("  • Detailed performance monitoring")


def main():
    """Main function"""
    try:
        asyncio.run(test_optimized_pipeline())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
